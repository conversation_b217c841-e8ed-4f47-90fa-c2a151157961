import { useState, useEffect } from 'react';
import { Box, Button, Checkbox, DatePicker2, Divider, Form, Input, Message, Radio } from '@alifd/next';
import Container from '../Container';
import NumberInput from '../NumberInput/NumberInput';
import styles from './index.module.scss';
import ProductPicker from '../ProductPicker';
import { showErrorMessageDialog } from '@/utils';
import { conditionNameMap } from './util';
import {
  CONDITION_TYPE,
  ORDER_CYCLE_TYPE,
  ORDER_STATUS,
} from '@/utils/constant';
import useMemberLevel from '@/hooks/useMemberLevel';

interface Props {
  onResolve: (value: any) => void;
  value?: any;
}

interface ConditionValue {
  enabled: boolean;
  value?: any;
  thresholdId?: number;
}

interface ThresholdState {
  crowdName: string;
  conditions: {
    [CONDITION_TYPE.NEW_CUSTOMER]: ConditionValue;
    [CONDITION_TYPE.OLD_CUSTOMER]: ConditionValue;
    [CONDITION_TYPE.LAST_ORDER_TIME]: ConditionValue;
    [CONDITION_TYPE.MEMBER_LEVEL]: ConditionValue;
    [CONDITION_TYPE.ORDER_CYCLE]: ConditionValue;
    [CONDITION_TYPE.ORDER_STATUS]: ConditionValue;
    [CONDITION_TYPE.TOTAL_ORDER_AMOUNT]: ConditionValue;
    [CONDITION_TYPE.TOTAL_ORDER_COUNT]: ConditionValue;
    [CONDITION_TYPE.PURCHASE_SPECIFIC_PRODUCT]: ConditionValue;
  };
}

const layout = {
  labelCol: { span: 3 },
  wrapperCol: { span: 14 },
};

// 门槛配置初始值
const initialState: ThresholdState = {
  crowdName: '',
  conditions: {
    // 新客户
    [CONDITION_TYPE.NEW_CUSTOMER]: { enabled: false, thresholdId: 1 },
    // 老客户
    [CONDITION_TYPE.OLD_CUSTOMER]: { enabled: false, thresholdId: 2 },
    // 最后一次下单时间
    [CONDITION_TYPE.LAST_ORDER_TIME]: { enabled: false, value: [], thresholdId: 3 },
    // 会员等级
    [CONDITION_TYPE.MEMBER_LEVEL]: { enabled: false, value: [], thresholdId: 4 },
    // 订单周期
    // type: 1: 在多少天内累计订单, 2: 指定时间内累计订单
    // days: 在多少天内累计订单
    // dateRange: 指定时间内累计订单
    [CONDITION_TYPE.ORDER_CYCLE]: {
      enabled: false, value: { type: ORDER_CYCLE_TYPE.DAYS, days: 1, dateRange: [] }, thresholdId: 5 },
    // 订单状态
    // value: 1: 已付款未关闭, 2: 已收货
    [CONDITION_TYPE.ORDER_STATUS]: { enabled: false, value: ORDER_STATUS.PAID_NOT_CLOSED, thresholdId: 6 },
    // 累计订单金额
    // min: 最小金额
    // max: 最大金额
    [CONDITION_TYPE.TOTAL_ORDER_AMOUNT]: { enabled: false, value: { min: 0, max: 0 }, thresholdId: 7 },
    // 累计订单数
    // min: 最小订单数
    // max: 最大订单数
    [CONDITION_TYPE.TOTAL_ORDER_COUNT]: { enabled: false, value: { min: 1, max: 1 }, thresholdId: 8 },
    // 购买指定商品
    // value: 商品列表
    [CONDITION_TYPE.PURCHASE_SPECIFIC_PRODUCT]: { enabled: false, value: [], thresholdId: 9 },
  },
};


export default function Threshold({ onResolve, value }: Props) {
  const [thresholdInfo, setThresholdInfo] = useState<ThresholdState>(value || initialState);
  const memberLevelList = useMemberLevel();
  const { conditions, crowdName } = thresholdInfo;
  const {
    [CONDITION_TYPE.NEW_CUSTOMER]: newCustomer,
    [CONDITION_TYPE.OLD_CUSTOMER]: oldCustomer,
    [CONDITION_TYPE.LAST_ORDER_TIME]: lastOrderTime,
    [CONDITION_TYPE.MEMBER_LEVEL]: memberLevel,
    [CONDITION_TYPE.ORDER_CYCLE]: orderCycle,
    [CONDITION_TYPE.ORDER_STATUS]: orderStatus,
    [CONDITION_TYPE.TOTAL_ORDER_AMOUNT]: totalOrderAmount,
    [CONDITION_TYPE.TOTAL_ORDER_COUNT]: totalOrderCount,
    [CONDITION_TYPE.PURCHASE_SPECIFIC_PRODUCT]: purchaseSpecificProduct,
  } = conditions;

  // 检查是否有订单相关条件被选中
  const hasOrderRelatedConditions =
    totalOrderAmount.enabled ||
    totalOrderCount.enabled ||
    purchaseSpecificProduct.enabled;

  // 自动启用/禁用订单周期和订单状态
  useEffect(() => {
    if (hasOrderRelatedConditions) {
      // 当有订单相关条件选中时，自动启用订单周期和订单状态
      setThresholdInfo(prev => ({
        ...prev,
        conditions: {
          ...prev.conditions,
          [CONDITION_TYPE.ORDER_CYCLE]: { ...prev.conditions[CONDITION_TYPE.ORDER_CYCLE], enabled: true },
          [CONDITION_TYPE.ORDER_STATUS]: { ...prev.conditions[CONDITION_TYPE.ORDER_STATUS], enabled: true },
        },
      }));
    }
    // 当没有订单相关条件选中时，只取消禁用状态，不自动取消选中
  }, [hasOrderRelatedConditions]);

  // 更新条件
  const updateCondition = (key: keyof ThresholdState['conditions'], enabled: boolean, value?: any) => {
    // 如果是订单周期或订单状态，且有订单相关条件被选中，则不允许手动修改
    if ((key === CONDITION_TYPE.ORDER_CYCLE || key === CONDITION_TYPE.ORDER_STATUS) && hasOrderRelatedConditions) {
      return;
    }

    setThresholdInfo(prev => ({
      ...prev,
      conditions: {
        ...prev.conditions,
        [key]: { enabled, thresholdId: prev.conditions[key].thresholdId, value: value ?? prev.conditions[key].value },
      },
    }));
  };

  // 更新条件值
  const updateConditionValue = (key: keyof ThresholdState['conditions'], value: any) => {
    setThresholdInfo(prev => ({
      ...prev,
      conditions: {
        ...prev.conditions,
        [key]: { ...prev.conditions[key], thresholdId: prev.conditions[key].thresholdId, value },
      },
    }));e
  };

  // 更新人群名称
  const updateCrowdName = (name: string) => {
    setThresholdInfo(prev => ({ ...prev, crowdName: name }));
  };

  // 验证用户条件
  const validateConditions = () => {
    const errors: string[] = [];

    if (!crowdName) {
      errors.push('请输入人群名称');
    }

    // 检查是否至少选择了一个用户条件
    const hasEnabledCondition = Object.values(conditions).some(condition => condition.enabled);
    if (!hasEnabledCondition) {
      errors.push('用户条件至少选择一个');
    }

    // 检查订单周期或订单状态选中时，必须选择相关订单条件
    const orderCycleOrStatusEnabled = orderCycle.enabled || orderStatus.enabled;
    if (orderCycleOrStatusEnabled && !hasOrderRelatedConditions) {
      errors.push('选择订单周期或订单状态时，请至少选择累计订单金额、累计订单数或购买指定商品中的至少一个');
    }

    // 检查已启用条件的值是否完整
    Object.entries(conditions).forEach(([key, condition]) => {
      if (!condition.enabled) return;

      const conditionName = getConditionName(key);

      switch (key) {
        case CONDITION_TYPE.LAST_ORDER_TIME:
          if (!condition.value || !Array.isArray(condition.value) || condition.value.length === 0) {
            errors.push(`${conditionName}的时间范围不能为空`);
          } else if (!new Date(condition.value[0]).getTime() || !new Date(condition.value[1]).getTime()) {
            errors.push(`${conditionName}的时间范围不能为空`);
          }
          break;
        case CONDITION_TYPE.MEMBER_LEVEL:
          if (!condition.value || !Array.isArray(condition.value) || condition.value.length === 0) {
            errors.push(`${conditionName}至少选择一个等级`);
          }
          break;
        case CONDITION_TYPE.ORDER_CYCLE:
          if (condition.value) {
            if (condition.value.type === ORDER_CYCLE_TYPE.DAYS &&
              (!condition.value.days || condition.value.days <= 0)) {
              errors.push(`${conditionName}的天数必须大于0`);
            }
            if (condition.value.type === ORDER_CYCLE_TYPE.DATE_RANGE &&
              (!condition.value.dateRange || condition.value.dateRange.length === 0)) {
              errors.push(`${conditionName}的时间范围不能为空`);
            } else if (condition.value.type === ORDER_CYCLE_TYPE.DATE_RANGE &&
               (!new Date(condition.value.dateRange[0]).getTime() ||
                !new Date(condition.value.dateRange[1]).getTime())) {
              errors.push(`${conditionName}的时间范围不能为空`);
            }
          } else {
            errors.push(`${conditionName}配置不能为空`);
          }
          break;
        case CONDITION_TYPE.TOTAL_ORDER_AMOUNT:
          if (!condition.value || (condition.value.min === 0 && condition.value.max === 0)) {
            errors.push(`${conditionName}的金额范围不能为空`);
          } else if (condition.value.min > condition.value.max) {
            errors.push(`${conditionName}的最小金额不能大于最大金额`);
          }
          break;
        case CONDITION_TYPE.TOTAL_ORDER_COUNT:
          if (!condition.value || (condition.value.min === 0 && condition.value.max === 0)) {
            errors.push(`${conditionName}的订单数范围不能为空`);
          } else if (condition.value.min > condition.value.max) {
            errors.push(`${conditionName}的最小订单数不能大于最大订单数`);
          }
          break;
        case CONDITION_TYPE.PURCHASE_SPECIFIC_PRODUCT:
          if (!condition.value || !Array.isArray(condition.value) || condition.value.length === 0) {
            errors.push(`${conditionName}至少选择一个商品`);
          }
          break;
      }
    });

    return errors;
  };

  // 获取条件名称
  const getConditionName = (key: string) => {
    return conditionNameMap[key] || key;
  };

  const onSave = () => {
    // 进行自定义验证
    const conditionErrors = validateConditions();
    if (conditionErrors.length > 0) {
      showErrorMessageDialog(conditionErrors);
      return;
    }
    onResolve({
      crowdName,
      conditions: conditions,
    });
  };

  const onCancel = () => {
    onResolve(null);
  };

  return (
    <Form labelAlign="left" {...layout} className={styles.checkboxWrapper}>
      <Form.Item name="crowdName" label="人群名称" required requiredMessage="请输入人群名称">
        <Input maxLength={20} showLimitHint value={crowdName} onChange={updateCrowdName} />
      </Form.Item>
      <Message type="warning">注意：所有条件默认取交集，即：满足所配置全部条件的用户视为可参与活动</Message>
      <Divider />
      <Container title="用户条件" style={{ margin: 0, padding: '0 10px 0' }}>
        <Form.Item>
          <Checkbox
            checked={newCustomer.enabled}
            onChange={(checked) => updateCondition(CONDITION_TYPE.NEW_CUSTOMER, checked)}
          >
            新客户(近3月内无支付成功订单即为新客，支付后退款视为未支付)
          </Checkbox>
        </Form.Item>
        <Form.Item>
          <Checkbox
            checked={oldCustomer.enabled}
            onChange={(checked) => updateCondition(CONDITION_TYPE.OLD_CUSTOMER, checked)}
          >
            老客户(近3月内有支付成功订单即为老客，支付后退款视为未支付)
          </Checkbox>
        </Form.Item>
        <Form.Item>
          <Checkbox
            checked={lastOrderTime.enabled}
            onChange={(checked) => updateCondition(CONDITION_TYPE.LAST_ORDER_TIME, checked)}
          >
            最后一次下单时间
          </Checkbox>
          <DatePicker2.RangePicker
            showTime
            x-if={lastOrderTime.enabled}
            value={lastOrderTime.value}
            onChange={(value) => updateConditionValue(CONDITION_TYPE.LAST_ORDER_TIME, value)}
          />
        </Form.Item>
        <Form.Item>
          <Checkbox
            checked={memberLevel.enabled}
            onChange={(checked) => updateCondition(CONDITION_TYPE.MEMBER_LEVEL, checked)}
          >
            会员等级
          </Checkbox>
          <Checkbox.Group
            x-if={memberLevel.enabled}
            value={memberLevel.value}
            onChange={(value) => updateConditionValue(CONDITION_TYPE.MEMBER_LEVEL, value)}
          >
            {memberLevelList.map((item) => (
              <Checkbox key={item.grade} value={item.grade}>{item.gradeName}</Checkbox>
            ))}
          </Checkbox.Group>
        </Form.Item>
      </Container>
      <Container title="交易条件" style={{ margin: 0, padding: '0 10px 0' }}>
        <Form.Item>
          <Box direction="row" align={'start'} spacing={8}>
            <Checkbox
              checked={orderCycle.enabled}
              disabled={hasOrderRelatedConditions}
              onChange={(checked) => updateCondition(CONDITION_TYPE.ORDER_CYCLE, checked)}
            >
              订单周期
            </Checkbox>
            <Radio.Group
              x-if={orderCycle.enabled}
              direction={'ver'}
              value={orderCycle.value?.type}
              onChange={(type) =>
                updateConditionValue(CONDITION_TYPE.ORDER_CYCLE, { ...orderCycle.value, type })}
            >
              <Radio value={ORDER_CYCLE_TYPE.DAYS}>
                在 <NumberInput
                  value={orderCycle.value?.days || 1}
                  min={1}
                  style={{ margin: '0 8px' }}
                  onChange={(days) =>
                    updateConditionValue(CONDITION_TYPE.ORDER_CYCLE, { ...orderCycle.value, days })}
                />天内累计订单
              </Radio>
              <Radio value={ORDER_CYCLE_TYPE.DATE_RANGE}>
                指定时间内累计订单
                <DatePicker2.RangePicker
                  showTime
                  style={{ marginLeft: 10 }}
                  value={orderCycle.value?.dateRange}
                  onChange={(dateRange) =>
                    updateConditionValue(CONDITION_TYPE.ORDER_CYCLE, { ...orderCycle.value, dateRange })}
                />
              </Radio>
            </Radio.Group>
          </Box>
        </Form.Item>
        <Form.Item>
          <Checkbox
            checked={orderStatus.enabled}
            disabled={hasOrderRelatedConditions}
            onChange={(checked) => updateCondition(CONDITION_TYPE.ORDER_STATUS, checked)}
          >
            订单状态
          </Checkbox>
          <Radio.Group
            x-if={orderStatus.enabled}
            value={orderStatus.value}
            onChange={(value) => updateConditionValue(CONDITION_TYPE.ORDER_STATUS, value)}
          >
            <Radio value={ORDER_STATUS.PAID_NOT_CLOSED}>已付款未关闭</Radio>
            <Radio value={ORDER_STATUS.RECEIVED}>已收货</Radio>
          </Radio.Group>
        </Form.Item>
        <Form.Item>
          <Checkbox
            checked={totalOrderAmount.enabled}
            onChange={(checked) => updateCondition(CONDITION_TYPE.TOTAL_ORDER_AMOUNT, checked)}
          >
            累计订单金额
          </Checkbox>
          <span x-if={totalOrderAmount.enabled} className={styles.numberInputWrapper}>
            <NumberInput
              min={0}
              value={totalOrderAmount.value?.min}
              onChange={(min: number) =>
                updateConditionValue(CONDITION_TYPE.TOTAL_ORDER_AMOUNT, { ...totalOrderAmount.value, min })}
            />
            <span>-</span>
            <NumberInput
              min={0}
              value={totalOrderAmount.value?.max}
              onChange={(max: number) =>
                updateConditionValue(CONDITION_TYPE.TOTAL_ORDER_AMOUNT, { ...totalOrderAmount.value, max })}
            />
          </span>
        </Form.Item>
        <Form.Item>
          <Checkbox
            checked={totalOrderCount.enabled}
            onChange={(checked) =>
              updateCondition(CONDITION_TYPE.TOTAL_ORDER_COUNT, checked)}
          >
            累计订单数
          </Checkbox>
          <span x-if={totalOrderCount.enabled} className={styles.numberInputWrapper}>
            <NumberInput
              min={1}
              value={totalOrderCount.value?.min}
              onChange={(min: number) =>
                updateConditionValue(CONDITION_TYPE.TOTAL_ORDER_COUNT, { ...totalOrderCount.value, min })}
            />
            <span>-</span>
            <NumberInput
              min={1}
              value={totalOrderCount.value?.max}
              onChange={(max: number) =>
                updateConditionValue(CONDITION_TYPE.TOTAL_ORDER_COUNT, { ...totalOrderCount.value, max })}
            />
          </span>
        </Form.Item>
        <Form.Item>
          <Box direction="row" align={'start'}>
            <Checkbox
              checked={purchaseSpecificProduct.enabled}
              onChange={(checked) => updateCondition(CONDITION_TYPE.PURCHASE_SPECIFIC_PRODUCT, checked)}
            >
              购买指定商品
            </Checkbox>
            <ProductPicker
              x-if={purchaseSpecificProduct.enabled}
              min={1}
              max={200}
              selectedItems={purchaseSpecificProduct.value}
              onSelectedProducts={(products: any) =>
                updateConditionValue(CONDITION_TYPE.PURCHASE_SPECIFIC_PRODUCT, products)}
            />
          </Box>
        </Form.Item>
      </Container>
      <Divider />
      <Box justify="center" align="center" direction="row" spacing={8}>
        <Button type="primary" style={{ width: 100 }} onClick={onSave}>保存</Button>
        <Button onClick={onCancel}>取消</Button>
      </Box>
    </Form>
  );
}
