import { Box, Button, Dialog, Table, Message } from '@alifd/next';
import { useState, useEffect } from 'react';
import editPrize from '@/components/editPrize/editPrize';
import choosePrize from '@/components/choosePrize/choosePrize';
import { getPrizeTypeLabel, PrizeTypeEnum } from '@/utils';
import { ACTIVITY_STATUS } from '@/utils/constant';
import { activityCustomizeActivityMinusLotteryNum } from '@/api/b';


const initPrizeItem = {
  lotteryName: '谢谢参与',
  lotteryType: PrizeTypeEnum.THANKS.value,
  lotteryValue: '',
  prizeNum: '',
  dayLimitType: '',
  awardLimitType: '',
  awardLimitCount: '',
  probability: 0,
};
const initPrizeList = new Array(8).fill(initPrizeItem);

interface Props {
  prizeList: any[];
  onPrizeChange?: (newPrizeList: any[]) => void;
  status: [number, string];
  disabledTabs: number[];
  activityId: string;
}
const WarningContent = () => {
  return (
    <Box direction={'column'} spacing={8} align={'center'}>
      <div>编辑奖品将释放已冻结库存</div>
      <div><span className="text-red">请务必重新完整保存活动</span>，是否继续</div>
    </Box>
  );
};

// 抽奖类表格
export default function PrizeWheelTable({ prizeList, onPrizeChange, status, disabledTabs, activityId }: Props) {
  const [activityStatus, operationType] = status;
  const [tableList, setTableList] = useState<any[]>(prizeList);
  const isNoStart = activityStatus === ACTIVITY_STATUS.NOT_STARTED;
  const isEnded = activityStatus === ACTIVITY_STATUS.ENDED;
  const isView = operationType === 'view';
  const isEdit = operationType === 'edit';

  // 同步外部prizeList的变化
  useEffect(() => {
    if (prizeList.length > 0) {
      if (prizeList.length === 8) {
        setTableList(prizeList);
      } else {
        // 不足8个使用initPrizeItem补全8个
        const newList = [...prizeList, ...new Array(8 - prizeList.length).fill(initPrizeItem)];
        setTableList(newList);
      }
    } else {
      setTableList(initPrizeList);
    }
    // setTableList(prizeList.length > 0 ? prizeList : initPrizeList);
  }, [prizeList]);

  // 更新奖品列表并通知父组件
  const updatePrizeList = (newList: any[]) => {
    setTableList(newList);
    onPrizeChange?.(newList);
  };

  // 交换奖品位置
  const movePrize = (index: number, type: 'up' | 'down') => {
    const swap = ([a, b]) => [b, a];
    const newList = [...tableList];
    const targetIndex = type === 'up' ? index - 1 : index + 1;
    [newList[index], newList[targetIndex]] = swap([newList[index], newList[targetIndex]]);
    updatePrizeList(newList);
  };

  const handleBackStock = async (record: any) => {
    try {
      if (record.lotteryType === PrizeTypeEnum.PRACTICALITY.value && isNoStart && isEdit && record.prizeId) {
        await activityCustomizeActivityMinusLotteryNum({
          lotteryId: record.prizeId,
          num: record.prizeNum,
          activityId,
        } as any);
      }
    } catch (error) {
      console.error('回撤库存失败：', error);
    }
  };

  // 编辑奖品
  const editPrizeHandler = async (index: number, record: any) => {
    const isThanks = record.lotteryType === PrizeTypeEnum.THANKS.value;
    const isPhysical = record.lotteryType === PrizeTypeEnum.PRACTICALITY.value;
    const needBackStock = isNoStart && record.prizeId && isPhysical;
    // 封装编辑奖品的核心逻辑
    const handleEdit = async () => {
      try {
        let result: any;
        await handleBackStock(record);
        // 如果是谢谢惠顾类型，调用choosePrize
        if (isThanks) {
          result = await choosePrize({
            activityType: 'wheel',
            disabledTabs,
            status,
          });
        } else {
          // 其他类型使用editPrize
          result = await editPrize({
            editPrizeInfo: record,
            disabledTabs, // 可根据需要配置禁用的奖品类型
            field: null, // 如果需要表单字段验证，可以传入field
            activityType: 'wheel',
            status,
          });
        }

        if (result) {
          console.log('编辑奖品结果：', result);
          const newList = [...tableList];
          newList[index] = result;
          updatePrizeList(newList);
          Message.success('编辑奖品成功');
        }
      } catch (error) {
        console.error('编辑奖品失败：', error);
        Message.error('编辑奖品失败');
      }
    };

    // 针对已开始活动且有ID的奖品且不是谢谢惠顾类型，需要先确认
    if (needBackStock) {
      Dialog.confirm({
        title: '提示',
        content: <WarningContent />,
        onOk: () => {
          // 先将列表中对应奖品重置为谢谢参与
          const newList = [...tableList];
          newList[index] = { ...initPrizeItem };
          updatePrizeList(newList);
          // 然后执行编辑逻辑
          handleEdit();
          return true; // 明确返回true来关闭对话框
        },
      });
    } else {
      // 其他情况直接编辑
      await handleEdit();
    }
  };

  // 重置奖品为谢谢惠顾
  const removePrize = async (index: number, record: any) => {
    const hasId = record.prizeId;

    // 封装删除奖品的核心逻辑
    const handleRemove = async () => {
      try {
        // 如果是进行中的活动且奖品有ID，需要调用释放库存接口
        if (isNoStart && hasId) {
          await handleBackStock(record);
        }
        const newList = [...tableList];
        // 重置为谢谢惠顾类型，保留原始ID等信息
        newList[index] = { ...initPrizeItem };
        updatePrizeList(newList);
        Message.success('奖品删除成功');
      } catch (error) {
        console.error('删除奖品失败：', error);
        Message.error('删除奖品失败');
      }
    };

    // 如果是进行中的活动且奖品有ID，需要提醒用户删除将释放已冻结库存
    const content = isNoStart && hasId
      ? '删除奖品将释放已冻结库存，请务必重新完整保存活动，是否继续？'
      : '确定要将此奖品删除吗？此次操作不可恢复';

    Dialog.confirm({
      title: '删除奖品',
      content,
      onOk: handleRemove,
    });
  };

  // 渲染操作按钮
  const renderAction = (_: boolean, index: number, record: any) => {
    const isProcessing = activityStatus === ACTIVITY_STATUS.IN_PROGRESS;
    const isCreate = operationType === 'add';
    const isCopy = operationType === 'copy';
    const isThanks = record.lotteryType === PrizeTypeEnum.THANKS.value;

    // 编辑按钮显示逻辑：活动进行中编辑且不是谢谢参与时展示 或 活动创建/复制/编辑未开始活动时展示
    const showEditButton = (isProcessing && !isThanks) || isCreate || isCopy || isNoStart;

    // 其他按钮显示逻辑：仅在活动创建、复制和编辑未开始活动时展示
    const showOtherButton = isCreate || isCopy || isNoStart;

    return (
      <Box direction="row" align="center" spacing={8}>
        {showEditButton && (
          <Button type={'primary'} text onClick={() => editPrizeHandler(index, record)}>编辑</Button>
        )}
        {showOtherButton && (
          <Box direction="row" align="center" spacing={8}>
            <Button x-if={record.lotteryType !== PrizeTypeEnum.THANKS.value} type={'primary'} text onClick={() => removePrize(index, record)}>删除</Button>
            <Button
              type={'primary'}
              text
              disabled={index === 0}
              onClick={() => movePrize(index, 'up')}
            >
              上移
            </Button>
            <Button
              type={'primary'}
              text
              disabled={index === tableList.length - 1}
              onClick={() => movePrize(index, 'down')}
            >
              下移
            </Button></Box>
        )}

      </Box>
    );
  };

  return (
    <Table dataSource={tableList}>
      <Table.Column title="奖项" cell={(_, index) => index + 1} />
      <Table.Column title="奖品名称" dataIndex="lotteryName" cell={(value) => value || '-'} />
      <Table.Column
        title="奖品类型"
        dataIndex="lotteryType"
        cell={(value) => getPrizeTypeLabel(value)}
      />
      <Table.Column
        title="单位数量"
        cell={(value, index, record) => {
          if (!record.lotteryType) return '-';
          if (record.lotteryType === PrizeTypeEnum.MEMBER_POINT.value) {
            return record.lotteryValue;
          } else {
            return 1;
          }
        }}
      />
      <Table.Column title="发奖总数" dataIndex="prizeNum" cell={value => value || '-'} />
      <Table.Column
        title="每日发放限额"
        cell={(value, index, record) => {
          if (record.dayLimitType === 1) {
            return record.dayLimitCount;
          } else {
            return '-';
          }
        }}
      />
      <Table.Column title="中奖概率" dataIndex="probability" cell={(value) => (value ? `${value}%` : '-')} />
      <Table.Column
        title="单人中奖次数上限"
        cell={(value, index, record) => {
          if (record.awardLimitType === 1) {
            return record.awardLimitCount;
          } else {
            return '-';
          }
        }}
      />
      <Table.Column
        title="奖品图"
        dataIndex="showImage"
        cell={(value) => {
          if (value) {
            return <img src={value} alt="奖品" style={{ width: 50, height: 50, objectFit: 'cover' }} />;
          } else {
            return '-';
          }
        }}
      />
      {
        (!isEnded && !isView) && <Table.Column title="操作" cell={renderAction} />
      }
    </Table>
  );
}
