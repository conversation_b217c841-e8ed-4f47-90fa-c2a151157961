import { useState, useEffect } from 'react';
import { Box, Button, DatePicker2, Tab, Table, Pagination, Message, Loading, Icon } from '@alifd/next';
import { echartsGetTab, echartsGetPromotionRecord, echartsGetTreeData } from '@/api/b';
import { LineChart } from 'bizcharts';
import dayjs from 'dayjs';
import { useLocation } from '@ice/runtime/router';
import Container from '@/components/Container';
import usePagination from '@/hooks/usePagination';
import styles from './index.module.scss';

export default function ActivityPromotionDataPage() {
  const location = useLocation();
  const { record } = location.state || {};
  const { activityId } = record || {};

  const [tabs, setTabs] = useState<any>([]);
  const [activeTab, setActiveTab] = useState('');

  const [activityInfo, setActivityInfo] = useState<any>({});
  const [statisticsTime, setStatisticsTime] = useState('');

  const [startDate, setStartDate] = useState<any>(null);
  const [endDate, setEndDate] = useState<any>(null);

  const [chartData, setChartData] = useState<any>([]);

  const [tableColumns, setTableColumns] = useState<any>([]);
  const [tableData, setTableData] = useState([]);

  const [chartLoading, setChartLoading] = useState(false);
  const [tableLoading, setTableLoading] = useState(false);

  // 使用分页Hook
  const pagination = usePagination(1, 10, (current, size) => {
    if (activityId && activeTab) {
      fetchTableData({
        pageNum: current,
        pageSize: size,
      });
    }
  });

  useEffect(() => {
    if (!record?.startTime || !record?.endTime) return;

    const now = dayjs();
    const start = dayjs(record.startTime);
    const end = now.isBefore(dayjs(record.endTime)) ? now.endOf('day') : dayjs(record.endTime);
    setStartDate(start.format('YYYY-MM-DD HH:mm:ss'));
    setEndDate(end.format('YYYY-MM-DD HH:mm:ss'));
  }, [record]);

  useEffect(() => {
    if (!activityId) return;
    loadTabs().then();
  }, [activityId]);

  useEffect(() => {
    // if (!activeTab) return;
    fetchChartData().then();
    fetchTableData().then();
  }, []);

  const loadTabs = async () => {
    try {
      const res = await echartsGetTab({ activityId } as any);
      let tabList: any = res || [];
      // 过滤掉无效的 tab（title 和 type 不能为空）
      tabList = tabList.filter(tab => tab.title && tab.type);
      setTabs(tabList);
      if (tabList.length) {
        setActiveTab(tabList[0].type);
      }
    } catch (err) {
      console.error('加载 Tab 失败', err);
      Message.error(err.message);
    }
  };

  const fetchChartData = async (params?: any) => {
    setChartLoading(true);
    try {
      const res = await echartsGetTreeData({
        activityId,
        type: activeTab,
        startDate: startDate ? dayjs(startDate).format('YYYY-MM-DD') : '',
        endDate: endDate ? dayjs(endDate).format('YYYY-MM-DD') : '',
        ...params,
      } as any);
      setChartData(Array.isArray(res) ? res : []);
    } catch (err) {
      console.error('加载图表数据失败', err);
    } finally {
      setChartLoading(false);
    }
  };

  const fetchTableData = async (params?: any) => {
    setTableLoading(true);
    try {
      const res: any = await echartsGetPromotionRecord({
        activityId,
        type: activeTab,
        pageNum: pagination.current,
        pageSize: pagination.pageSize,
        startDate: startDate ? dayjs(startDate).format('YYYY-MM-DD') : '',
        endDate: endDate ? dayjs(endDate).format('YYYY-MM-DD') : '',
        ...params,
      });
      const { data = {}, statisticsTime, activityStartTime, activityEndTime, activityName } = res;
      setStatisticsTime(statisticsTime);
      setActivityInfo({ activityName, activityStartTime, activityEndTime });
      const parsedCols = JSON.parse(data.title.replace(/,]$/, ']') || '[]');
      setTableColumns(parsedCols);
      setTableData(data.list || []);
      pagination.setTotal(data.total || 0);
    } catch (err) {
      console.error('加载表格数据失败', err);
      Message.error(err.message);
    } finally {
      setTableLoading(false);
    }
  };

  const handleTabChange = key => {
    setActiveTab(key);
    // pagination.changePage(1);
    fetchTableData({
      pageNum: 1,
      type: key,
    }).then();
    fetchChartData({
      type: key,
    }).then();
  };

  const handleSearch = async () => {
    pagination.changePage(1);
    await fetchChartData();
  };

  return (
    <Box spacing={16} className={styles.promotionDataPage}>
      <Container>
        <Loading visible={chartLoading} style={{ width: '100%', height: '100%' }}>
          <Box spacing={20}>
            <Box className={styles.titleSection} direction="row" justify="space-between" align="center">
              <Box>
                <h3 className={styles.mainTitle}>渠道推广效果趋势  <Icon
                  type="refresh"
                  style={{ color: 'var(--primary-color)', fontSize: 16, width: 16, height: 16, cursor: 'pointer' }}
                  onClick={() => {
                  pagination.changePage(1);
                  fetchChartData();
                  // fetchTableData();
                }}
                /></h3>
                <Box direction="row" className={styles.infoContainer}>
                  <Box direction="row" align="center" className={styles.infoItem}>
                    <span className={styles.infoLabel}>活动名称:</span>
                    <span className={styles.infoValue}>{activityInfo.activityName || '--'}</span>
                  </Box>
                  <Box direction="row" align="center" className={styles.infoItem}>
                    <span className={styles.infoLabel}>活动时间:</span>
                    <span className={styles.infoValueNormal}>
                      {activityInfo.activityStartTime} ~ {activityInfo.activityEndTime}
                    </span>
                  </Box>
                </Box>
              </Box>
              <Box className={styles.statsBox}>
                <span className={styles.statsLabel}>
                  统计截止：
                  <span className={styles.statsValue}>
                    {statisticsTime && activityInfo.activityEndTime
                      ? dayjs(statisticsTime).isAfter(activityInfo.activityEndTime)
                        ? activityInfo.activityEndTime
                        : statisticsTime
                      : '--'}
                  </span>
                </span>
              </Box>
            </Box>

            <Box direction="row" align="center" className={styles.filterSection}>
              <Box direction="row" spacing={10}>
                <DatePicker2.RangePicker
                  hasClear={false}
                  outputFormat="YYYY-MM-DD HH:mm:ss"
                  value={[startDate, endDate]}
                  onChange={([start, end]) => {
                    setStartDate(start);
                    setEndDate(end);
                  }}
                  disabledDate={date => {
                    const start = dayjs(activityInfo.activityStartTime);
                    const end = dayjs(activityInfo.activityEndTime);
                    return date.isBefore(start, 'day') || date.isAfter(end, 'day');
                  }}
                />
                <Button type="primary" className={styles.searchButton} onClick={handleSearch}>查询</Button>
              </Box>
              <Tab
                shape="capsule"
                activeKey={activeTab}
                onChange={handleTabChange}
              >
                {tabs.map(tab => (
                  <Tab.Item
                    title={tab.title}
                    key={tab.type}
                  />
                ))}
              </Tab>
            </Box>
            {/* 折线图 */}
            {chartData.length > 0 ? (
              <Box className={styles.chartContainer}>
                <LineChart
                  data={chartData}
                  autoFit
                  xField="opdate"
                  yField="uv"
                  smooth
                  height={300}
                  point={{ size: 4 }}
                />
              </Box>
            ) : (
              <Box
                align="center"
                justify="center"
                className={styles.emptyChart}
              >
                暂无数据
              </Box>
            )}
          </Box>
        </Loading>
      </Container>


      <Container>
        <div className={styles.title}>从各渠道进入活动人数</div>
        <Loading visible={tableLoading} style={{ width: '100%', height: '100%' }} >
          <Box spacing={16}>
            <Table
              dataSource={tableData}
              hasBorder
            >
              {tableColumns.map(col => (
                <Table.Column
                  key={col.dataIndex}
                  title={col.name}
                  dataIndex={col.dataIndex}
                />
              ))}
            </Table>
            <Box direction="row" justify="flex-end">
              <Pagination
                {...pagination.paginationProps}
                shape={'arrow-only'}
                pageSizeSelector="dropdown"
              />
            </Box>
          </Box>
        </Loading>

      </Container>
    </Box>
  );
}
