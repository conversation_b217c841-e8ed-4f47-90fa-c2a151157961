.promotionDataPage {
  .titleSection {
    border-bottom: 1px solid #e8e6e6;
    padding-bottom: 16px;
    margin-bottom: 8px;

    .mainTitle {
      margin: 0 0 12px 0;
      font-size: 18px;
      color: #000;
      font-weight: 600;
    }

    .infoContainer {
      display: flex;
      flex-direction: row;
      gap: 24px;

      .infoItem {
        display: flex;
        align-items: center;

        .infoLabel {
          color: var(--color-text-secondary);
          margin-right: 8px;
          font-size: 13px;
        }

        .infoValue {
          font-weight: 500;
          font-size: 14px;
          color: var(--primary-color)
        }

        .infoValueNormal {
          font-size: 14px;
          color: var(--primary-color)
        }
      }
    }

    .statsBox {
      background-color: var(--primary-color-lighter);
      padding: 8px 16px;
      border-radius: 4px;

      .statsLabel {
        color: var(--color-text-secondary);
        font-size: 13px;
      }

      .statsValue {
        color: var(--primary-color);
        font-weight: 500;
        margin-left: 4px;
      }
    }
  }

  .filterSection {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: 16px 0 24px 0;
    background-color: var(--color-background-light);
    padding: 12px 16px;
    border-radius: 4px;
    gap: 16px;

    .searchButton {
      background-color: var(--primary-color);
      border-color: var(--primary-color);
    }
  }

  .chartContainer {
    border: 1px solid var(--color-border-light);
    border-radius: 4px;
    padding: 16px;
    background-color: #fff;
  }

  .emptyChart {
    height: 300px;
    background-color: #fff;
    border: 1px solid var(--color-border-light);
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
.title {
  font-weight: bold;
  margin-bottom: 20px;
}
