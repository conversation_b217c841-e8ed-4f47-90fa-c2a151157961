import { useEffect, useState } from 'react';
import { Box, Table, Input, Select, Button, Pagination, Message } from '@alifd/next';
import TimeDiff from '@/components/TimeDiff/TimeDiff';
import { activityGetActivityList } from '@/api/b';
import { activityGetAllActivityTypes } from '@/api/common';


export default function ChooseActivityModalContent({ onResolve }) {
  const [activityName, setActivityName] = useState<any>('');
  const [activityType, setActivityType] = useState<any>(0);
  const [activityTypeList, setActivityTypeList] = useState<any[]>([]);
  const [tableData, setTableData] = useState([]);
  const [pageNo, setPageNo] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [total, setTotal] = useState(0);

  const fetchList = async () => {
    try {
      const res: any = await activityGetActivityList({
        pageNo,
        pageSize,
        activityName,
        activityType,
        activityStatus: 0,
        createDateStart: '',
        createDateEnd: '',
      } as any);
      setTableData(res?.list);
      setTotal(res?.total);
    } catch (err) {
      console.error(err);
      Message.error(err.message);
    } finally {
    }
  };

  useEffect(() => {
    fetchList();
  }, [pageNo, pageSize]);

  useEffect(() => {
    getActivityTypeList();
  }, []);

  const getActivityTypeList = async () => {
    const res: any = await activityGetAllActivityTypes();
    setActivityTypeList(res);
  };

  const handleSearch = () => {
    setPageNo(1);
    fetchList();
  };


   const getActivityTypeLabel = activityType => {
    for (const key in activityTypeList) {
      if (activityTypeList[key].value === activityType) {
        return activityTypeList[key].label;
      }
    }
    return '--';
  };
  return (
    <Box spacing={16}>
      <Box
        direction="row"
        spacing={16}
        align="center"
        margin={[0, 0, 16, 0]}
      >
        <Input
          placeholder="活动名称"
          value={activityName}
          onChange={setActivityName}
          style={{ width: 200 }}
        />
        <Select
          label="活动类型"
          placeholder="活动类型"
          value={activityType}
          onChange={setActivityType}
          style={{ width: 200 }}
        >
          <Select.Option value={0}>全部类型</Select.Option>
          {activityTypeList?.map(item => (
            <Select.Option
              key={item.value}
              value={item.value}
            >
              {item.label}
            </Select.Option>
          ))}
        </Select>
        <Button
          type="primary"
          onClick={handleSearch}
        >
          查询
        </Button>
      </Box>
      <Table
        dataSource={tableData}
        hasBorder
      >
        <Table.Column
          title="活动名称"
          dataIndex="activityName"
        />
        <Table.Column
          title="活动类型"
          dataIndex="activityType"
          cell={value => getActivityTypeLabel(value)}
        />
        <Table.Column
          title="活动时间"
          cell={(_, __, record) => (
            <TimeDiff
              startTime={record.startTime}
              endTime={record.endTime}
            />
          )}
        />
        <Table.Column
          title="操作"
          cell={(_, __, record) => (
            <Button
              type="primary"
              text
              onClick={() => onResolve(record)}
            >
              选择活动
            </Button>
          )}
        />
      </Table>
      <Pagination
        current={pageNo}
        pageSize={pageSize}
        total={total}
        onChange={p => setPageNo(p)}
        onPageSizeChange={size => {
          setPageSize(size);
          setPageNo(1);
        }}
        pageSizeSelector="dropdown"
        pageSizePosition="end"
        totalRender={t => `共${t}条`}
      />
    </Box>
  );
}
