import { Box, Form, Field, Button, DatePicker2, Message, Table, Input } from '@alifd/next';
import { getActivityTypeLabel } from '@/utils';
import TimeDiff from '@/components/TimeDiff/TimeDiff';
import chooseActivity from '../chooseActivity/chooseActivity';
import { planCreateActivityPlan } from '@/api/b';

export default function CreatePlanModalContent({ cardSize, onResolve }) {
  const createPlanField = Field.useField();

  const handleOk = () => {
    createPlanField.validate(async (errors, values: any) => {
      if (!errors) {
        const [start, end] = values.timeRange || [];
        const payload = {
          ...values,
          startTime: start,
          endTime: end,
          planType: cardSize,
        };
        try {
          await planCreateActivityPlan(payload);
          Message.success('创建投放计划成功');
          onResolve(true);
        } catch (err) {
          console.error('createActivityPlan error:', err);
          Message.error(err.message);
        }
      }
    });
  };

  const handleCancel = () => {
    onResolve(null);
  };

  // 点击"选择活动"
  const handleChooseActivity = async () => {
    const activity: any = await chooseActivity();
    if (activity) {
      createPlanField.setValue('id', activity.id);
      createPlanField.setValue('activityId', activity.activityId);
      createPlanField.setValue('activityName', activity.activityName);
      createPlanField.setValue('activityType', activity.activityType);
      createPlanField.setValue('startTime', activity.startTime);
      createPlanField.setValue('endTime', activity.endTime);
      createPlanField.setValue('crowdName', activity.crowdName);
    }
  };

  const activityId = createPlanField.getValue('activityId');
  const activityName = createPlanField.getValue('activityName');
  const activityType = createPlanField.getValue('activityType');
  const startTime = createPlanField.getValue('startTime');
  const endTime = createPlanField.getValue('endTime');
  const crowdName = createPlanField.getValue('crowdName');

  return (
    <Box>
      <Form field={createPlanField}>
        <Form.Item
          name="activityId"
          label="投放活动"
          required
          requiredMessage="活动ID不能为空"
        >
          {activityId ? (
            <Table
              dataSource={[
                {
                  activityId,
                  activityName,
                  activityType,
                  startTime,
                  endTime,
                  crowdName,
                },
              ]}
              hasBorder={false}
              primaryKey="activityId"
            >
              <Table.Column
                title="活动名称"
                dataIndex="activityName"
              />
              <Table.Column
                title="活动类型"
                dataIndex="activityType"
                cell={val => getActivityTypeLabel(val)}
              />
              <Table.Column
                title="活动时间"
                cell={(_, __, record) => (
                  <TimeDiff
                    startTime={record.startTime}
                    endTime={record.endTime}
                  />
                )}
              />
              <Table.Column
                title="投放人群"
                dataIndex="crowdName"
              />
              <Table.Column
                title="操作"
                cell={() => (
                  <Button
                    type="primary"
                    text
                    onClick={handleChooseActivity}
                  >
                    重新选择
                  </Button>
                )}
              />
            </Table>
          ) : (
            <Button
              type="primary"
              onClick={handleChooseActivity}
            >
              选择活动
            </Button>
          )}
        </Form.Item>

        <Form.Item
          name="planName"
          label="投放计划名称"
          required
          requiredMessage="请输入投放计划名称"
        >
          <Input showLimitHint maxLength={15} style={{ width: '100%' }} />
        </Form.Item>

        <Form.Item
          name="planCouponName"
          label="投放券名称"
          required
          requiredMessage="请输入投放券名称"
        >
          <Input showLimitHint maxLength={15} style={{ width: '100%' }} />
        </Form.Item>

        <Form.Item
          name="planCouponDesc"
          label="使用说明"
          required
          requiredMessage="请输入投放计划名称"
        >
          <Input showLimitHint maxLength={15} style={{ width: '100%' }} />
        </Form.Item>

        <Form.Item
          name="timeRange"
          label="投放时间"
          required
          requiredMessage="请选择投放时间"
          extra={<div className="form-extra">
            <div>1.开始时间：可选范围 = 当前时间+1小时 至 当前时间+30天</div>
            <div>2.结束时间：可选范围 = 开始时间+1小时 至 开始时间+30天</div>
          </div>}
        >
          <DatePicker2.RangePicker
            showTime
            format="YYYY-MM-DD HH:mm:ss"
            outputFormat="YYYY-MM-DD HH:mm:ss"
            timePanelProps={{ defaultValue: ['00:00:00', '23:59:59'] }}
            style={{ width: '100%' }}
          />
        </Form.Item>

        {/* <Form.Item */}
        {/*   name="orderBy" */}
        {/*   label="投放优先级" */}
        {/*   required */}
        {/*   requiredMessage="请输入优先级" */}
        {/* > */}
        {/*   <NumberInput */}
        {/*     min={1} */}
        {/*     style={{ width: '100%' }} */}
        {/*   /> */}
        {/* </Form.Item> */}

        <Box
          direction="row"
          justify="center"
          spacing={12}
          margin={[20, 0, 0, 0]}
        >
          <Button onClick={handleCancel}>取消</Button>
          <Button
            type="primary"
            onClick={handleOk}
          >
            保存
          </Button>
        </Box>
      </Form>
    </Box>
  );
}
