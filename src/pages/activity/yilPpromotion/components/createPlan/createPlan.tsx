import { Dialog } from '@alifd/next';
import React from 'react';
import CreatePlanModalContent from './CreatePlanModalContent';
import { addDialogRef } from '@/utils/dialogMapper';

export default function createPlan({ cardSize }) {
  return new Promise(resolve => {
    const dialogRef = Dialog.show({
      v2: true,
      title: '新建投放计划',
      width: 800,
      centered: true,
      closeMode: ['close'],
      content: (
        <CreatePlanModalContent
          cardSize={cardSize}
          onResolve={result => {
            resolve(result);
            dialogRef.hide();
          }}
        />
      ),
      footer: false,
    });
    addDialogRef(dialogRef);
  });
}
