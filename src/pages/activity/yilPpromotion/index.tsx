import { useEffect, useState } from 'react';
import {
  Box,
  Balloon,
  Button,
  Input,
  Select,
  Table,
  Pagination,
  Tab,
  Message,
  Radio, Dialog,
} from '@alifd/next';
import { copyText, getActivityTypeLabel } from '@/utils';
import TimeDiff from '@/components/TimeDiff/TimeDiff';
import NumberInput from '@/components/NumberInput/NumberInput';
import HelpTooltip from '@/components/HelpTooltip/HelpTooltip';
import { history, useLocation } from 'ice';
import usePagination from '@/hooks/usePagination';

import createPlan from './components/createPlan/createPlan';
import dayjs from 'dayjs';
import {
  planGetActivityPlanList,
  planCancelPlan,
  planUpdateOrderBy,
  planUpdateStartTime,
  planCreateActivityPlan,
  planGetLiveRoomConfigList, planUpdateLiveRoomConfig,
} from '@/api/b';
import Container from '@/components/Container';
import chooseActivity from '@/pages/activity/promotion/components/chooseActivity/chooseActivity';
import { addDialogRef } from '@/utils/dialogMapper';

const cardSizeTabs = [
  { key: 1, label: '通用活动列表' },
  { key: 2, label: '直播间小雪花' },
  { key: 3, label: '抖音首页推荐（feed流）' },
];
const STATUS_OPTIONS = [
  { label: '全部', value: 0 },
  { label: '未开始', value: 1 },
  { label: '投放中', value: 2 },
  { label: '已结束', value: 3 },
];


const dialogInfo = {
  1: {
    img: 'https://novae.oss-cn-zhangjiakou.aliyuncs.com/vf/%E9%80%9A%E7%94%A8%E5%88%97%E8%A1%A8%E9%A1%B5.png',
    info: '1. 用户将进入小程序列表页，列表页展示已配置且符合门槛的活动入口；\n2. 如该用户只有1个活动符合，则自动跳转到活动详情页。',
    rule: '',
  },
  2: {
    img: 'https://novae.oss-cn-zhangjiakou.aliyuncs.com/douyin/images/snow.png',
    info: '1. 通过直播间小雪花进入小程序，可进入该列表中发布的活动；\n2. 直播间小雪花入口支持配置多个活动，配置多个活动时将按照跳转模式进行跳转；',
    rule: '',
  },
  3: {
    img: 'https://novae.oss-cn-zhangjiakou.aliyuncs.com/douyin/images/feed.png',
    info: '抖音首页推荐计划是抖音平台打造的小程序引流手段，借助权益券的形式向消费者推\n送小程序权益。这种券并非用于电商交易，而是在推荐环节发挥作用。\n首页推荐将以"权益卡"的样式呈现给用户，用户领取"权益卡"后，便会直接进入小程\n序，此时会展示推荐计划里所设定的小程序页面。',
    rule: '在每一个特定的时刻，仅有一条推送计划会被执行。若同时有多条处于执行中的计\n划，那么会按照生效时间的先后顺序来安排推荐顺序，先生效的计划会优先得到推\n荐，等其生效时间截止后，才会依次推荐后续的计划内容。',
  },
};

const RadioGroup = Radio.Group;


export default function LiveCardIndexPage() {
  const [cardSize, setCardSize] = useState<any>(1);
  const [activityName, setActivityName] = useState<any>('');
  const [status, setStatus] = useState<any>(0);
  const [activityPlanList, setActivityPlanList] = useState([]);
  const [currentRadio, setCurrentRadio] = useState<any>(null);
  const [jumpMode, setJumpMode] = useState<any>(1);
  const [loading, setLoading] = useState<boolean>(false);

  // 使用分页Hook
  const pagination = usePagination(1, 10, (current, size) => {
    loadData({
      pageNum: current,
      pageSize: size,
    });
  });

  const location = useLocation();
  const { tab } = location.state || { tab: 1 };

  const [livingRoom, setLivingRoom] = useState<any>([]);

  // 组件加载时根据路由的tab参数设置cardSize
  useEffect(() => {
    if (tab) {
      setCardSize(Number(tab));
    }
  }, []);

  // 整合后的数据加载函数
  const loadData = async (obj?: any) => {
    setLoading(true);
    const current = Number(obj?.planType) || Number(cardSize);
    console.log('current', current);
    try {
      // 加载活动计划列表
      const params: any = {
        pageNum: pagination.current,
        pageSize: pagination.pageSize,
        activityName,
        status,
        planType: current,
        ...obj,
      };
      // 如果是直播间小雪花标签，先加载直播间信息
      const data = await planGetLiveRoomConfigList();
      console.log('current', data);
      const result = data.map(e => ({
        ...e,
        label: e.liveName,
        value: e.id,
      }));

      if (result && result.length > 0 && currentRadio === null) {
        setCurrentRadio(result[0].id);
      }

      const selectedRoom = result.find(e => e.id === currentRadio);
      if (selectedRoom) {
        setJumpMode(selectedRoom.jumpType);
      }

      setLivingRoom(result);
      if (current == 2 && currentRadio) {
        const currentRoom = getCurrentLivingRoom();
        if (currentRoom) {
          params.liveRoomId = currentRoom.liveRoomId;
        }
      }

      const res: any = await planGetActivityPlanList(params);
      setActivityPlanList(res.list || []);
      pagination.setTotal(res.total || 0);
    } catch (err) {
      console.error(err);
      Message.error(err.message);
    } finally {
      setLoading(false);
    }
  };

  // 获取当前选中的直播间信息
  const getCurrentLivingRoom = () => {
    return livingRoom && currentRadio ? livingRoom.find(e => e.id === currentRadio) || null : null;
  };

  // 移除原来分散的fetchLivingInfo和fetchPlanList调用
  useEffect(() => {
    loadData();
  }, []);

  const handleReset = () => {
    setActivityName('');
    setStatus(0);
    pagination.reset();

    // 手动调用查询确保使用重置后的查询条件
    loadData({
      pageNum: 1,
      pageSize: 10,
      activityName: '',
      status: 0,
      planType: Number(cardSize),
    });
  };

  const getStatus = (start, end) => {
    const now = dayjs();
    if (dayjs(end).isBefore(now)) return 3; // 已结束
    if (dayjs(start).isAfter(now)) return 1; // 未开始
    if (dayjs(start).isBefore(now) && dayjs(end).isAfter(now)) return 2; // 投放中
    return 3; // 已结束
  };

  const handleStart = ({ id }) => {
    const dialogRef = Dialog.confirm({
      title: '立即投放',
      content: '确定要立即开始该投放计划吗？',
      onOk: async () => {
        setLoading(true);
        try {
          await planUpdateStartTime({
            id,
          });
          await loadData();
          Message.success('投放计划已开始!');
        } catch (e) {
          Message.error('投放失败');
        } finally {
          setLoading(false);
        }
      },
    });
    addDialogRef(dialogRef);
  };

  const handleCancel = record => {
    Dialog.confirm({
      title: '取消投放',
      content: '确定要取消该投放计划吗？',
      onOk: async () => {
        setLoading(true);
        try {
          await planCancelPlan({ id: record.id });
          await loadData();
        } catch (e) {
          Message.error('取消投放失败');
        } finally {
          setLoading(false);
        }
      },
    });
  };

  const renderStatus = record => {
    const status = getStatus(record.startTime, record.endTime);
    const map = {
      1: { label: '未开始', className: 'status-before' },
      2: { label: '投放中', className: 'status-normal' },
      3: { label: '已结束', className: 'status-disabled' },
    };
    return <span className={map[status].className}>{map[status].label}</span>;
  };

  const renderActions = record => {
    const status = getStatus(record.startTime, record.endTime);
    if (status === 1) {
      return (
        <Button
          type="primary"
          text
          onClick={() => handleStart(record)}
        >
          立即投放
        </Button>
      );
    }
    if (status == 2) {
      return (
        <Box direction="row" spacing={10}>
          <Button
            type="primary"
            text
            onClick={() => handleCancel(record)}
          >
            取消投放
          </Button>
        </Box>
      );
    }
    return (
      <div />
    );
  };

  const addPlan = async () => {
    if (cardSize === 3) {
      const result = await createPlan({ cardSize });
      if (result) {
        await loadData();
      }
    } else {
      try {
        const data: any = await chooseActivity();
        setLoading(true);
        const params: any = {
          id: data.id,
          startTime: data.startTime,
          endTime: data.endTime,
          planType: cardSize,
        };
        if (cardSize == 2) {
          const currentRoom = getCurrentLivingRoom();
          if (currentRoom) {
            params.liveRoomId = currentRoom.liveRoomId;
          }
        }
        await planCreateActivityPlan(params);
        Message.success('新建投放计划成功');
        await loadData();
      } catch (e) {
        Message.error(e.message);
      } finally {
        setLoading(false);
      }
    }
  };

  const handleSearch = () => {
    pagination.changePage(1);
  };

  return (
    <Box spacing={16}>
      <Container>
        <Box spacing={16}>
          <Box
            direction="row"
            align="center"
            justify="space-between"
          >
            <Box
              direction="row"
              spacing={16}
              wrap
            >
              <Input
                label="活动名称"
                placeholder="请输入活动名称"
                value={activityName}
                onChange={setActivityName}
                style={{ width: 240 }}
              />
              <Select
                label="投放状态"
                value={status}
                onChange={setStatus}
                style={{ width: 160 }}
              >
                {STATUS_OPTIONS.map(opt => (
                  <Select.Option
                    key={opt.value}
                    value={opt.value}
                  >
                    {opt.label}
                  </Select.Option>
                ))}
              </Select>
              <Button
                type="primary"
                onClick={handleSearch}
              >
                查询
              </Button>
              <Button onClick={handleReset}>重置</Button>
            </Box>
            <Button
              type="primary"
              onClick={() => addPlan()}
            >
              {cardSize === 1 ? '添加投放活动' : '新建投放计划'}
            </Button>
          </Box>

          {/* 表格 */}
          <Table
            dataSource={activityPlanList}
            loading={loading}
            hasBorder
          >
            <Table.Column
              x-if={cardSize !== 3}
              title="活动名称"
              dataIndex="activityName"
            />
            <Table.Column
              x-if={cardSize !== 3}
              title="活动类型"
              dataIndex="activityType"
              cell={value => getActivityTypeLabel(value)}
            />
            <Table.Column
              x-if={cardSize === 3}
              title="投放计划名称"
              dataIndex="planName"
            />
            <Table.Column
              x-if={cardSize === 3}
              title="投放计划ID"
              dataIndex="planId"
            />
            <Table.Column
              x-if={cardSize === 3}
              title="投放券名称"
              dataIndex="planCouponName"
            />
            <Table.Column
              x-if={cardSize === 3}
              title="关联投放活动"
              dataIndex="activityName"
            />
            <Table.Column
              title="投放状态"
              cell={(_, __, record) => renderStatus(record)}
            />
            <Table.Column
              title="操作"
              cell={(_, __, record) => renderActions(record)}
            />
          </Table>
          <Box direction="row" justify="flex-end">
            <Pagination
              {...pagination.paginationProps}
              shape={'arrow-only'}
              pageSizeSelector="dropdown"
              pageSizePosition="end"
              totalRender={t => `共${t}条`}
            />
          </Box>
        </Box>
      </Container>
    </Box>
  );
}
