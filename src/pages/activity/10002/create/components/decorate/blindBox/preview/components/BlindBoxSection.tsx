import styles from '../index.module.scss';
import { useBoxList } from '../hooks/useBoxList';

interface BlindBoxSectionProps {
  main: any;
  onBoxClick?: (idx: number) => void;
}

export default function BlindBoxSection({ main }: BlindBoxSectionProps) {
  const icons = [main.boxIcon1, main.boxIcon2, main.boxIcon3];
  console.log('main', icons);
  const { boxList, handleBoxClick } = useBoxList(icons);
  return (
    <div className={styles.box}>
      <img className={styles.boxBg} src={main.boxBg} alt="" />
      <div className={styles.boxList}>
        {boxList.map((box, idx) => (
          <div
            key={idx}
            className={
              box.status === 'selected'
                ? `${styles.boxItem} ${styles.selected}`
                : styles.boxItem
            }
            onClick={() => handleBoxClick(idx)}
            style={{ cursor: box.status === 'opened' ? 'not-allowed' : 'pointer' }}
          >
            <img className={styles.boxIcon} src={box.icon} alt="" />
            {box.status === 'opened' ? (
              <img className={styles.boxBtn} src={main.boxBtnOpened || main.boxBtnUnselected} alt="已拆开" />
            ) : box.status === 'selected' ? (
              <img className={styles.boxBtn} src={main.boxBtnSelected || main.boxBtnUnselected} alt="已选中" />
            ) : (
              <img className={styles.boxBtn} src={main.boxBtnUnselected} alt="未选中" />
            )}
          </div>
        ))}
      </div>
      <div className={styles.boxFooter}>
        <div className={styles.openBtn}>拆盲盒</div>
        <div className={styles.remainText}>剩余抽奖机会:<span>3</span>次</div>
      </div>

      <div className={styles.exchangeBtn}>
        <img src="https://novae.oss-cn-zhangjiakou.aliyuncs.com/vf/%E7%9B%B2%E7%9B%92%E8%A3%85%E4%BF%AE/%E5%88%87%E6%8D%A2.png" alt="" />
        换一批
      </div>
    </div>
  );
}